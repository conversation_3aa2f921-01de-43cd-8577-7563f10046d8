"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  AddGroupForm,
  group,
} from "@/components/settings/groups/group-management";
import { ArrowLeft } from "lucide-react";

export default function AddGroupPage() {
  const router = useRouter();

  const handleSubmit = (newGroup: group) => {
    // Here you would typically make an API call to save the group
    // For now, we'll just log the group and redirect back to the groups page
    console.log("New group created:", newGroup);

    // TODO: Add API call here
    // Example:
    // try {
    //   await fetch('/api/groups', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(newGroup)
    //   });
    //   // Show success message
    //   router.push("/app/settings/groups");
    // } catch (error) {
    //   // Handle error
    // }

    // For now, just redirect back to groups page
    router.push("/app/settings/groups");
  };

  const handleCancel = () => {
    router.push("/app/settings/groups");
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Groups
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Add New Group</h1>
        <p className="text-gray-600 mt-2">
          Create a new group with the required information.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <AddGroupForm onSubmit={handleSubmit} />

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} className="w-full">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
