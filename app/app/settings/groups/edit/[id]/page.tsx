"use client";

import { useRouter, usePara<PERSON> } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { EditGroupForm } from "@/components/settings/groups/edit-group";
import { group } from "@/components/settings/groups/group-management";
import { ArrowLeft } from "lucide-react";

export default function EditGroupPage() {
  const router = useRouter();
  const params = useParams();
  const groupId = params.id as string;

  const [group, setGroup] = useState<group | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real application, you would fetch the group data from an API
    // For now, we'll simulate this with mock data
    const fetchGroup = () => {
      // Mock group data - in reality, you'd fetch this from your API
      const id = parseInt(groupId);
      const mockGroups = [
        { id: 1, groupname: "admin" },
        { id: 2, groupname: "superadmin" },
        { id: 3, groupname: "sales" },
        { id: 4, groupname: "support" },
      ];

      const mockGroup = mockGroups.find((grp) => grp.id === id);

      if (mockGroup) {
        setGroup(mockGroup);
      }
      setLoading(false);
    };

    if (groupId) {
      fetchGroup();
    }
  }, [groupId]);

  const handleSubmit = (updatedGroup: group) => {
    // Here you would typically make an API call to update the group
    // For now, we'll just log the updated group and redirect back
    console.log("Group updated:", updatedGroup);

    // TODO: Add API call here
    // Example:
    // try {
    //   await fetch(`/api/groups/${groupId}`, {
    //     method: 'PUT',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(updatedGroup)
    //   });
    //   // Show success message
    //   router.push("/app/settings/groups");
    // } catch (error) {
    //   // Handle error
    // }

    // For now, just redirect back to groups page
    router.push("/app/settings/groups");
  };

  const handleCancel = () => {
    router.push("/app/settings/groups");
  };

  if (loading) {
    return (
      <div className="p-6 max-w-2xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading group data...</div>
        </div>
      </div>
    );
  }

  if (!group) {
    return (
      <div className="p-6 max-w-2xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">Group not found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Groups
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Edit Group</h1>
        <p className="text-gray-600 mt-2">
          Update group information for <strong>{group.groupname}</strong>.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <EditGroupForm group={group} onSubmit={handleSubmit} />

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} className="w-full">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
