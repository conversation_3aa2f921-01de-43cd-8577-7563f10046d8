// page.tsx
"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  GroupTable,
  group,
} from "@/components/settings/groups/group-management";
import { Button } from "@/components/ui/button";

export default function GroupManagementPage() {
  const router = useRouter();

  const [groups, setGroups] = useState<group[]>([
    { id: 1, groupname: "admin" },
    { id: 2, groupname: "superadmin" },
    { id: 3, groupname: "sales" },
    { id: 4, groupname: "support" },
  ]);
  const [search, setSearch] = useState("");
  const filteredGroups = groups.filter((group) =>
    group.groupname.toLowerCase().includes(search.toLowerCase())
  );

  // add max-w-4xl ml later
  return (
    <div className="p-6 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Group Management Portal</h1>
        <input
          type="text"
          placeholder="Search groups."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="mb-4 w-[250px] p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
        />
        <Button
          className="ml-20"
          onClick={() => router.push("/app/settings/groups/add")}
        >
          Add New Group
        </Button>
      </div>

      <GroupTable
        groups={filteredGroups}
        onDelete={(id) => setGroups(groups.filter((group) => group.id !== id))}
      />
    </div>
  );
}
