"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { UserTable } from "@/components/settings/users/user-management";
import { Button } from "@/components/ui/button";
import { User } from "@/components/settings/users/add-user";

export default function UserManagementPage() {
  const router = useRouter();

  // Generate 100 users
  const [users, setUsers] = useState<User[]>(
    Array.from({ length: 100 }, (_, i) => {
      const id = i + 1;
      const isAdmin = id % 10 === 0;
      return {
        id,
        username: isAdmin ? `admin${id}` : `user${id}`,
        email: `user${id}@example.com`,
        role: isAdmin ? "admin" : "user",
        password: isAdmin ? `Admin@${id}` : `User@${id}`,
        organization: "Malla Telecom",
      };
    })
  );

  const [search, setSearch] = useState("");
  const filteredUsers = users.filter((user) =>
    user.username.toLowerCase().includes(search.toLowerCase())
    || user.email.toLowerCase().includes(search.toLowerCase())
    || user.organization.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="p-6 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">User Management Portal</h1>
        <input
          type="text"
          placeholder="Search users."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="mb-4 w-[250px] p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
        />
        <Button
          className="ml-20"
          onClick={() => router.push("/app/settings/users/add")}
        >
          Add New User
        </Button>
      </div>

      <UserTable
        users={filteredUsers}
        onDelete={(id) => setUsers(users.filter((user) => user.id !== id))}
      />

    </div>
  );
}
