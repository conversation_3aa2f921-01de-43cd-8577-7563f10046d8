"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  AddOrganizationForm,
  organization,
} from "@/components/settings/organizations/organization-management";
import { ArrowLeft } from "lucide-react";

export default function AddOrganizationPage() {
  const router = useRouter();

  const handleSubmit = (newOrganization: organization) => {
    // Here you would typically make an API call to save the organization
    // For now, we'll just log the organization and redirect back to the organizations page
    console.log("New organization created:", newOrganization);

    // TODO: Add API call here
    // Example:
    // try {
    //   await fetch('/api/organizations', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(newOrganization)
    //   });
    //   // Show success message
    //   router.push("/app/settings/organizations");
    // } catch (error) {
    //   // Handle error
    // }

    // For now, just redirect back to organizations page
    router.push("/app/settings/organizations");
  };

  const handleCancel = () => {
    router.push("/app/settings/organizations");
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Organizations
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Add New Organization</h1>
        <p className="text-gray-600 mt-2">
          Create a new organization with the required information.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <AddOrganizationForm onSubmit={handleSubmit} />

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} className="w-full">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
