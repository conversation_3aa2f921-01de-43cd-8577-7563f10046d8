"use client";

import { useRouter, useParams } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { EditOrganizationForm } from "@/components/settings/organizations/edit-organization";
import { organization } from "@/components/settings/organizations/organization-management";
import { ArrowLeft } from "lucide-react";

export default function EditOrganizationPage() {
  const router = useRouter();
  const params = useParams();
  const organizationId = params.id as string;

  const [organization, setOrganization] = useState<organization | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real application, you would fetch the organization data from an API
    // For now, we'll simulate this with mock data
    const fetchOrganization = () => {
      // Mock organization data - in reality, you'd fetch this from your API
      const id = parseInt(organizationId);
      const mockOrganizations = [
        { id: 1, organizationname: "Malla Telecom" },
        { id: 2, organizationname: "Shrestha Telecom" },
        { id: 3, organizationname: "Bajra Telecom" },
        { id: 4, organizationname: "Kathmandu Telecom" },
      ];

      const mockOrganization = mockOrganizations.find((org) => org.id === id);

      if (mockOrganization) {
        setOrganization(mockOrganization);
      }
      setLoading(false);
    };

    if (organizationId) {
      fetchOrganization();
    }
  }, [organizationId]);

  const handleSubmit = (updatedOrganization: organization) => {
    // Here you would typically make an API call to update the organization
    // For now, we'll just log the updated organization and redirect back
    console.log("Organization updated:", updatedOrganization);

    // TODO: Add API call here
    // Example:
    // try {
    //   await fetch(`/api/organizations/${organizationId}`, {
    //     method: 'PUT',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(updatedOrganization)
    //   });
    //   // Show success message
    //   router.push("/app/settings/organizations");
    // } catch (error) {
    //   // Handle error
    // }

    // For now, just redirect back to organizations page
    router.push("/app/settings/organizations");
  };

  const handleCancel = () => {
    router.push("/app/settings/organizations");
  };

  if (loading) {
    return (
      <div className="p-6 max-w-2xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading organization data...</div>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="p-6 max-w-2xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">Organization not found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Organizations
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Edit Organization</h1>
        <p className="text-gray-600 mt-2">
          Update organization information for{" "}
          <strong>{organization.organizationname}</strong>.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <EditOrganizationForm
          organization={organization}
          onSubmit={handleSubmit}
        />

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} className="w-full">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
