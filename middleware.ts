import { NextRequest, NextResponse } from 'next/server'

// Define protected routes that require authentication
const protectedRoutes = ['/app']
// Define public routes that don't require authentication
const publicRoutes = ['/', '/login']

export function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname
  
  // Check if the current path starts with any protected route prefix
  const isProtectedRoute = protectedRoutes.some(route => 
    path === route || path.startsWith(`${route}/`))
  
  // Get authentication status from localStorage (stored during login)
  const isAuthenticated = request.cookies.get('isLoggedIn')?.value === 'true'
  
  // If trying to access protected route without authentication, redirect to login
  if (isProtectedRoute && !isAuthenticated) {
    return NextResponse.redirect(new URL('/login', request.url))
  }
  
  // Allow access to the requested page
  return NextResponse.next()
}

// Configure which routes middleware should run on
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico|images).*)'],
}