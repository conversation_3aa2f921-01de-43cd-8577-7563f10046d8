"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Eye, EyeOff } from "lucide-react"; // Optional: use icons if available
import { Loader2 } from "lucide-react"; // For loading spinner

export default function UserLogin() {
  const router = useRouter();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // New state for loading
  const [errorMessage, setErrorMessage] = useState(""); // New state for error messages

  // Determine if the login button should be disabled
  // Now also includes isLoading to prevent multiple submissions
  const isLoginDisabled = !username || !password || isLoading;

  const handleLogin = async (e?: React.FormEvent) => {
    e?.preventDefault();
    setErrorMessage(""); // Clear previous error messages
    setIsLoading(true); // Set loading to true when login starts

    // Simulate an API call
    await new Promise((resolve) => setTimeout(resolve, 300)); // Simulate network delay

    if (username === "leomin" && password === "1234") {
      // Set cookie instead of localStorage
      document.cookie = "isLoggedIn=true; path=/; max-age=86400"; // 24 hours
      router.push("/app");
    } else {
      setErrorMessage("Invalid username or password."); // Set error message
    }
    setIsLoading(false); // Set loading to false after attempt
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-400 w-full max-w-screen overflow-x-hidden">
      <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-sm">
        <div className="relative h-2 w-full flex items-center justify-center mt-3 mb-6">
          <Image
            src="/images/workalaya-icon.png"
            alt="Workalaya Icon"
            width={70}
            height={70}
          />
        </div>
        <h2 className="text-xl font-semibold mb-4 text-center">Welcome Back</h2>
        <form onSubmit={handleLogin} className="space-y-4">
          <Input
            placeholder="Username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            // Add error state styling if desired
            className={errorMessage ? "border-red-200" : ""}
          />

          <div className="relative">
            <Input
              placeholder="Password"
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              // Add error state styling
              className={errorMessage ? "border-red-200" : ""}
            />
            <button
              type="button"
              onClick={() => setShowPassword((prev) => !prev)}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
              disabled={isLoading} // Disable eye icon button when loading
            >
              {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </button>
          </div>

          {/* Display error message */}
          {errorMessage && (
            <p className="text-red-500 text-sm text-center">{errorMessage}</p>
          )}

          <Button className="w-full" type="submit" disabled={isLoginDisabled}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isLoading ? "Logging in..." : "Login"}
          </Button>

          {/* Forgot Password Link */}
          <div className="text-center text-sm mt-4">
            <a
              href="#"
              className="text-gray-900 hover:underline"
              onClick={(e) => {
                e.preventDefault();
                alert("Forgot password functionality coming soon!");
              }}
            >
              Forgot Password?
            </a>
          </div>
        </form>
      </div>
    </div>
  );
}
