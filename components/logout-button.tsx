"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";

export default function LogoutButton() {
  const router = useRouter();
  
  const handleLogout = () => {
    // Clear the authentication cookie
    document.cookie = "isLoggedIn=false; path=/; max-age=0";
    // Redirect to login page
    router.push("/");
  };
  
  return (
    <Button onClick={handleLogout} variant="ghost">
      Logout
    </Button>
  );
}