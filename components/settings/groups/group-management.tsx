// components/group-management.tsx
"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export interface group {
  id: number;
  groupname: string;
}

// group Table Component
export function GroupTable({
  groups,
  onDelete,
}: {
  groups: group[];
  onDelete: (id: number) => void;
}) {
  return (
    // useEffect(() => {
    //   const fetchUsers = async () => {
    //     try {
    //       const response = await fetch("/api/users");

    //       // Check if response is empty or not JSON
    //       const text = await response.text();
    //       if (!text) {
    //         console.error("Empty response from server");
    //         return;
    //       }

    //       try {
    //         const data = JSON.parse(text);
    //         if (response.ok) setUsers(data);
    //       } catch (jsonError) {
    //         console.error("Invalid JSON response:", text, jsonError);
    //       }
    //     } catch (error) {
    //       console.error("Failed to fetch users:", error);
    //     }
    //   };
    //   fetchUsers();
    // }, []);

    // const handleAddUser = async (newUser: {
    //   username: string;
    //   password: string;
    //   role: string;
    // }) => {
    //   try {
    //     const response = await fetch("/api/users", {
    //       method: "POST",
    //       headers: { "Content-Type": "application/json" },
    //       body: JSON.stringify(newUser),
    //     });

    //     if (!response.ok) {
    //       const error = await response.json();
    //       throw new Error(error.error);
    //     }

    //     const createdUser = await response.json();
    //     setUsers([...users, createdUser]);
    //   } catch (error) {
    //     alert(error instanceof Error ? error.message : "Failed to add user");
    //   }
    // };

    // const handleDeleteUser = async (userId: number) => {
    //   if (window.confirm("Are you sure you want to delete this user?")) {
    //     try {
    //       const response = await fetch(`/api/users?id=${userId}`, {
    //         method: "DELETE",
    //       });

    //       if (!response.ok) {
    //         const error = await response.json();
    //         throw new Error(error.error);
    //       }

    //       setUsers(users.filter((user) => user.id !== userId));
    //     } catch (error) {
    //       alert(error instanceof Error ? error.message : "Failed to delete user");
    //     }
    //   }
    // };

    // const handlePasswordChange = async (
    //   currentPassword: string,
    //   newPassword: string
    // ) => {
    //   try {
    //     const response = await fetch("/api/auth/password", {
    //       method: "PUT",
    //       headers: { "Content-Type": "application/json" },
    //       body: JSON.stringify({ currentPassword, newPassword }),
    //     });

    //     if (!response.ok) {
    //       const error = await response.json();
    //       throw new Error(error.error);
    //     }

    //     return await response.json();
    //   } catch (error) {
    //     throw new Error(
    //       error instanceof Error ? error.message : "Password change failed"
    //     );
    //   }
    // };
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="rounded-2xl shadow-lg bg-white dark:bg-gray-900 overflow-hidden">
        <Table className="w-full">
          <TableHeader className="bg-gray-100 dark:bg-gray-800">
            <TableRow>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                S.N.
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Group Name
              </TableHead>
              <TableHead className="text-right text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {groups.map((group) => (
              <GroupRow key={group.id} group={group} onDelete={onDelete} />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// group Row Component
function GroupRow({
  group,
  onDelete,
}: {
  group: group;
  onDelete: (id: number) => void;
}) {
  const router = useRouter();

  return (
    <TableRow>
      <TableCell>{group.id}</TableCell>
      <TableCell>{group.groupname}</TableCell>
      <TableCell className="text-right space-x-2">
        <Button
          variant="outline"
          size="ss"
          onClick={() => router.push(`/app/settings/groups/edit/${group.id}`)}
        >
          Edit
        </Button>
        <Button
          variant="destructive"
          size="ss"
          onClick={() => onDelete(group.id)}
        >
          Delete
        </Button>
      </TableCell>
    </TableRow>
  );
}

// // Add group Form Component
export function AddGroupForm({
  onSubmit,
}: {
  onSubmit: (group: group) => void;
}) {
  const [newgroup, setNewgroup] = useState({
    groupname: "",
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...newgroup,
          groupname: newgroup.groupname.trim(),
        });
        setNewgroup({
          groupname: "",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Group Name"
        value={newgroup.groupname}
        onChange={(e) =>
          setNewgroup((prev) => ({ ...prev, groupname: e.target.value }))
        }
      />

      <Button type="submit" className="w-full">
        Add group
      </Button>
    </form>
  );
}
