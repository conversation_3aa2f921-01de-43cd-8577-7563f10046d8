"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { group } from "./group-management";

export interface EditGroupFormProps {
  group: group;
  onSubmit: (group: group) => void;
}

export function EditGroupForm({ group, onSubmit }: EditGroupFormProps) {
  const [editedGroup, setEditedGroup] = useState(group);

  const isValid = editedGroup.groupname.trim() !== "";

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid) {
      onSubmit({
        ...editedGroup,
        groupname: editedGroup.groupname.trim(),
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Group Name</label>
        <Input
          value={editedGroup.groupname}
          onChange={(e) =>
            setEditedGroup((prev) => ({ ...prev, groupname: e.target.value }))
          }
          placeholder="Group Name"
        />
      </div>

      <Button type="submit" className="w-full" disabled={!isValid}>
        Save Changes
      </Button>
    </form>
  );
}
