// components/organization-management.tsx
"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export interface organization {
  id: number;
  organizationname: string;
}

// organization Table Component
export function OrganizationTable({
  organizations,
  onDelete,
}: {
  organizations: organization[];
  onDelete: (id: number) => void;
}) {
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="rounded-2xl shadow-lg bg-white dark:bg-gray-900 overflow-hidden">
        <Table className="w-full">
          <TableHeader className="bg-gray-100 dark:bg-gray-800">
            <TableRow>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                S.N.
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Organization Name
              </TableHead>
              <TableHead className="text-right text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {organizations.map((organization) => (
              <OrganizationRow
                key={organization.id}
                organization={organization}
                onDelete={onDelete}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// organization Row Component
function OrganizationRow({
  organization,
  onDelete,
}: {
  organization: organization;
  onDelete: (id: number) => void;
}) {
  const router = useRouter();

  return (
    <TableRow>
      <TableCell>{organization.id}</TableCell>
      <TableCell>{organization.organizationname}</TableCell>
      <TableCell className="text-right space-x-2">
        <Button
          variant="outline"
          size="ss"
          onClick={() =>
            router.push(`/app/settings/organizations/edit/${organization.id}`)
          }
        >
          Edit
        </Button>
        <Button
          variant="destructive"
          size="ss"
          onClick={() => onDelete(organization.id)}
        >
          Delete
        </Button>
      </TableCell>
    </TableRow>
  );
}

// // Add organization Form Component
export function AddOrganizationForm({
  onSubmit,
}: {
  onSubmit: (organization: organization) => void;
}) {
  const [neworganization, setNeworganization] = useState({
    organizationname: "",
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...neworganization,
          organizationname: neworganization.organizationname.trim(),
        });
        setNeworganization({
          organizationname: "",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="organizationname"
        value={neworganization.organizationname}
        onChange={(e) =>
          setNeworganization((prev) => ({
            ...prev,
            organizationname: e.target.value,
          }))
        }
      />

      <Button type="submit" className="w-full">
        Add organization
      </Button>
    </form>
  );
}
