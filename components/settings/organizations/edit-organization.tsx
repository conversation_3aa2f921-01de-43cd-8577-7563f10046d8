"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { organization } from "./organization-management";

export interface EditOrganizationFormProps {
  organization: organization;
  onSubmit: (organization: organization) => void;
}

export function EditOrganizationForm({
  organization,
  onSubmit,
}: EditOrganizationFormProps) {
  const [editedOrganization, setEditedOrganization] = useState(organization);

  const isValid = editedOrganization.organizationname.trim() !== "";

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid) {
      onSubmit({
        ...editedOrganization,
        organizationname: editedOrganization.organizationname.trim(),
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">
          Organization Name
        </label>
        <Input
          value={editedOrganization.organizationname}
          onChange={(e) =>
            setEditedOrganization((prev) => ({
              ...prev,
              organizationname: e.target.value,
            }))
          }
          placeholder="Organization Name"
        />
      </div>

      <Button type="submit" className="w-full" disabled={!isValid}>
        Save Changes
      </Button>
    </form>
  );
}
